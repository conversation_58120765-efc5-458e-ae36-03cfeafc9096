#!/usr/bin/env bash
set -e errexit

ACCOUNT_ID="************"
REGION="us-east-1"
ENV="prod"
APPLICATION="points-approval-consumer"
VERSION="0.0.7"
TAG="$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/motivai/$APPLICATION:$VERSION"

sh ./build_image.sh

docker tag motivai/"$APPLICATION" "$TAG"
docker push "$TAG"

# helm install -n "$ENV" "$APPLICATION" -f ./helm/values.yaml -f ./helm/values-prod.yaml ./helm
