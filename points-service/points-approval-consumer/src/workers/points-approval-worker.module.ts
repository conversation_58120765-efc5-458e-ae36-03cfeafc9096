import { MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

import { PointsApprovalConsumer } from './consumers/points-approval.consumer';

import { MotivaiRabbitMQModule } from '../config/motivai-rabbitmq.module';
import { MainModule } from '../business/main/main.module';

@MotivaiNestCommon.Module({
  imports: [MotivaiRabbitMQModule, MainModule],
  providers: [PointsApprovalConsumer],
})
export class PointsApprovalWorkerModule {}
