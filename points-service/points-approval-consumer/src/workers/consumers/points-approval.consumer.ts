import { MessageHandlerErrorBehavior, QueueOptions, RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

import { PointsApprovalMessageModel } from '../models/points-approval-message.model';
import { MainService } from '../../business/main/services/main.service';

@MotivaiNestCommon.Injectable()
export class PointsApprovalConsumer {
  constructor(private readonly mainService: MainService) {}

  @RabbitSubscribe({
    queue: process.env.QUEUE_POINTS_APPROVED_CREDITS,
    routingKey: '',
    exchange: '',
    errorBehavior: MessageHandlerErrorBehavior.NACK,
    queueOptions: {
      deadLetterExchange: process.env.DL_POINTS_APPROVED_CREDITS,
    } as QueueOptions,
  })
  public async execute(messageModel: PointsApprovalMessageModel) {
    await this.mainService.processQueueMessage(messageModel?.temporaryPointId, messageModel?.batchId);
  }
}
