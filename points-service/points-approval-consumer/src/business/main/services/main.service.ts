import { generateNewObjectId, generateU<PERSON><PERSON>, LegacyAmount, LegacyDecimalAmount } from '@motivai/motivai-shared-mongoose';
import { CampaignPointsMetadataHeader } from '@motivai/motivai-shared-campaign-points-metadata-header';
import { Transaction, TransactionOrigin, TransactionType } from '@motivai/motivai-shared-transactions';
import { Mechanic, MechanicTotal } from '@motivai/motivai-shared-mechanics';
import { TemporaryPoints } from '@motivai/motivai-shared-temporary-points';
import { BatchImport } from '@motivai/motivai-shared-batch-import';
import { MotivaiException } from '@motivai/motivai-shared-exceptions';
import { MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';

import * as moment from 'moment';

import { CampaignPointsMetadataHeadersRepository } from '../../../business/uses-case/campaign-points-metadata-headers/repository/campaign-points-metadata-headers.repository';
import { MechanicsTotalsRepository } from '../../../business/uses-case/mechanics-totals/repository/mechanics-totals.repository';
import { TemporaryPointsRepository } from '../../../business/uses-case/temporary-points/repository/temporary-points.repository';
import { TransactionsRepository } from '../../../business/uses-case/transactions/repositories/transactions.repository';
import { CompaniesRepository } from '../../../business/uses-case/companies/repository/companies.repository';
import { CampaignsRepository } from '../../../business/uses-case/campaigns/repository/campaign.repository';
import { MechanicRepository } from '../../../business/uses-case/mechanic/repository/mechanic.repository';
import { BatchRepository } from '../../../business/uses-case/batch/repository/batch-import.repository';

import { ClientsIntegrationProxy } from '../../../business/uses-case/clients-integration/proxy/clients-integration.proxy';
import { BatchRaffleQueueApi } from '../../../business/uses-case/raffle-import-batch/proxy/batch-raffle-queue-api.proxy';

import { SendBatchToRaffleQueue } from '../../../business/uses-case/raffle-import-batch/models/send-batch-to-raffle-queue.model';
import { PersonType } from '../enums/person-type.enum';

const i = (message: string, tracking: string = null) => {
  MotivaiNestCommon.Logger.log(`${new Date()} - ${message} - ${tracking}`, MainService.name);
};

@MotivaiNestCommon.Injectable()
export class MainService {
  private readonly logger = new MotivaiNestCommon.Logger(MainService.name);

  constructor(
    private metadataRepository: CampaignPointsMetadataHeadersRepository,
    private clientsIntegrationRepository: ClientsIntegrationProxy,
    private mechanicsTotalsRepository: MechanicsTotalsRepository,
    private temporaryPointsRepository: TemporaryPointsRepository,
    private transactionRepository: TransactionsRepository,
    private batchRaffleQueueApi: BatchRaffleQueueApi,
    private companiesRepository: CompaniesRepository,
    private campaignsRepository: CampaignsRepository,
    private mechanicRepository: MechanicRepository,
    private batchRepository: BatchRepository,
    private amqpConnection: AmqpConnection,
  ) {}

  async processQueueMessage(temporaryPointId: string, batchId: string): Promise<Transaction> {
    try {
      i('Iniciando processo de criação da transação', temporaryPointId);
      if (!temporaryPointId) {
        throw MotivaiException.ofError('ID_PONTO_TEMPORARIO_NULO_VAZIO', 'Id do ponto temporário deve ser informado para processar');
      }

      // Recupera registro de ponto temporário
      i('Buscando registro de ponto temporário', temporaryPointId);
      const temporaryPoint = await this.temporaryPointsRepository.findById(temporaryPointId);

      if (!temporaryPoint) {
        throw MotivaiException.ofError('PONTO_TEMPORARIO_NAO_ENCONTRADO', `Ponto temporário não encontrado - ${temporaryPointId}`);
      }

      // Recupera informações da campanha
      const campaign = await this.campaignsRepository.findById(temporaryPoint.CampaignId);
      if (!campaign) {
        await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, 'Campanha não encontrada ou não configurada corretamente', 'CAMPAIGN_NOT_FOUND');

        throw MotivaiException.ofError('CAMPANHA_NAO_ENCONTRADA_NAO_CONFIGURADA', `Campanha não encontrada ou não configurada corretamente - ${temporaryPointId}`);
      }

      // Recupera mecanica do usuário já existente (combinação entre userId, campaignId e templateId)
      // Caso a regra de expiração de pontos seja por período (Period), geraremos uma mecanica por BatchId.
      // Cria uma nova caso não exista
      let mechanic: Mechanic = null;
      if (temporaryPoint.ExpirationType === 'FixedDate') {
        i('Buscando mecanica do usuário - Expiration Type = FixedDate (userId, campaignId e templateId)', temporaryPointId);
        mechanic = await this.mechanicRepository.findUserMechanic(temporaryPoint.UserId, temporaryPoint.CampaignId, temporaryPoint.MechanicId);
      } else if (temporaryPoint.ExpirationType === 'Period') {
        i('Buscando mecanica do usuário - Expiration Type = Period (userId, campaignId, templateId, batchId)', temporaryPointId);
        mechanic = await this.mechanicRepository.findUserMechanic(temporaryPoint.UserId, temporaryPoint.CampaignId, temporaryPoint.MechanicId, temporaryPoint.BatchImportId);
      }

      if (!mechanic) {
        i('Buscando template para criar mecanica', temporaryPointId);
        const template = await this.mechanicRepository.find(temporaryPoint.MechanicId);
        if (!template) {
          await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, 'Template da mecanica não encontrado', 'MECHANIC_TEMPLATE_NOT_FOUND');

          throw MotivaiException.ofError('TEMPLATE_MENICA_NAO_ENCONTRADO', `Template da mecânica não encontrado - ${temporaryPointId}`);
        }

        let expirationDate = temporaryPoint.ExpirationDate;
        if (temporaryPoint.ExpirationType === 'Period') {
          expirationDate = moment().add(temporaryPoint.ExpirationPeriod, 'days').toDate();
        }

        const userMechanic = {
          _id: generateUUID(),
          Description: template.Description,
          CampaignId: template.CampaignId,
          RankingId: template.RankingId,
          PointsConversionFactor: template.PointsConversionFactor,
          ExpirationDate: expirationDate,
          Boost: template.Boost,
          Distributable: template.Distributable,
          DistributableConfig: template.DistributableConfig,
          AllowOnlyInteger: template.AllowOnlyInteger,
          Active: template.Active,
          BatchId: temporaryPoint.BatchImportId,
          TemplateId: template._id,
          UserId: temporaryPoint.UserId,
          MechanicOriginId: temporaryPoint.BatchImportId,
          PointsExpirationType: temporaryPoint.ExpirationType,
          ExpirationPeriod: temporaryPoint.ExpirationPeriod,
          Type: 'Automatica',
          CreateDate: new Date(),
          UpdateDate: new Date(),
          TotalAmount: LegacyAmount.empty(),
          AvailableAmount: LegacyAmount.empty(),
          CanceledAmount: LegacyAmount.empty(),
          BlockedAmount: LegacyAmount.empty(),
          Origin: 'PointsImport',
          EnableRedemptionRules: template.EnableRedemptionRules,
          RedemptionRules: template.RedemptionRules,
        } as Mechanic;

        i('Criando mecanica', temporaryPointId);
        mechanic = await this.mechanicRepository.create(userMechanic);
      }

      if (!mechanic) {
        await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, 'Mecanica não encontrada ou criada corretamente', 'MECHANIC_NOT_FOUND');

        throw MotivaiException.ofError('MECANICA_NAO_ENCONTRADA', `Mecânica não encontrada ou criada corretamente - ${temporaryPointId}`);
      }

      // Cria transação (utilizando o index unique no campo TemporaryPointId)
      // Em caso de falha, não prossegue com o incremento do saldo
      // Caso a transação já tenha sido criada, recupera a existente e pula o passo de criação
      i('Criando transação', temporaryPointId);
      let transaction: Transaction = null;
      if (!temporaryPoint.TransactionCreated) {
        const trans = {
          _id: generateUUID(),
          Active: true,
          SorterId: generateNewObjectId(),
          MechanicId: mechanic._id,
          TransactionOrigin: TransactionOrigin.Import,
          TransactionType: temporaryPoint.PointsAmount > 0 ? TransactionType.Credit : TransactionType.Debt,
          UserParticipantCampaignId: temporaryPoint.UserParticipantId,
          CampaignId: temporaryPoint.CampaignId,
          Document: temporaryPoint.ParticipantDocument,
          UserId: temporaryPoint.UserId,
          ClientId: temporaryPoint.ClientId,
          PersonType: this.handlePersonType(temporaryPoint.ParticipantDocument),
          TransactionDescription: temporaryPoint.TransactionDescription,
          TransactionDate: new Date(),
          TotalAmount: {
            Currency: temporaryPoint.CurrencyAmount,
            Points: temporaryPoint.PointsAmount,
          },
          Parametrizations: temporaryPoint.Params || {},
          BatchId: temporaryPoint.BatchImportId,
          TemporaryPointId: temporaryPointId,
          ClientTransactionCode: temporaryPoint.ClientTransactionCode || temporaryPoint.TransactionOriginId,
          CreateDate: new Date(),
          ExtraData: {
            Headers: [],
            Body: [],
          },
        } as Transaction;

        // Monta Extra data caso necessário
        if (temporaryPoint.ExtraData) {
          trans.ExtraData = {
            Headers: temporaryPoint.ExtraData.Headers,
            Body: temporaryPoint.ExtraData.Body,
          };

          let meta = await this.metadataRepository.getById(temporaryPoint.CampaignId);
          if (!meta) {
            meta = await this.metadataRepository.createMetadata({
              _id: temporaryPoint.CampaignId,
              CampaignId: temporaryPoint.CampaignId,
              CreateDate: new Date(),
              UpdateDate: new Date(),
              DisplayOrientation: 'HORIZONTAL',
              HeadersDetails: [],
            } as CampaignPointsMetadataHeader);
          }

          for (const body of temporaryPoint.ExtraData.Body) {
            const existingMeta = meta.HeadersDetails.find((h) => h.Property === body.Property);
            if (!existingMeta) {
              await this.metadataRepository.pushHeader(temporaryPoint.CampaignId, {
                Active: true,
                Description: body.Header,
                Property: body.Property,
                Position: 99,
              });
            }
          }
        }

        // Monta extra data com resultado da distribuição
        if (mechanic.Distributable) {
          trans.ExtraData = {
            Headers: [...trans.ExtraData.Headers, 'Total', 'Distribuível', 'Utilizável'],
            Body: [
              ...trans.ExtraData.Body,
              {
                Origin: 'PointsImport',
                Header: 'Total',
                Property: '',
                Value: temporaryPoint.PointsAmount.toString(),
              },
              {
                Origin: 'PointsImport',
                Header: 'Distribuível',
                Property: '',
                Value: temporaryPoint.DistributablePointsAmount.toString(),
              },
              {
                Origin: 'PointsImport',
                Header: 'Utilizável',
                Property: '',
                Value: temporaryPoint.AvailablePointsAmount.toString(),
              },
            ],
          };
        }

        try {
          transaction = await this.transactionRepository.create(trans);
        } catch (err) {
          // Verifica se é erro de duplicidade
          if (err && err.code && err.code.toString() === '11000') {
            i('Possivel duplicidade de transação encontrada, buscando transação gerada anteriormente', temporaryPointId);
            transaction = await this.transactionRepository.findByTemporaryPointId(temporaryPointId);
          } else {
            await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, err.message, 'UNEXPECTED_ERROR');

            throw MotivaiException.ofError('UNEXPECTED_ERROR', err.message || err, err);
          }
        }

        // Se não localizou a transação, não prossegue com o processo
        if (!transaction) {
          await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, 'Transação não foi criada corretamente', 'TRANSACTION_NOT_CREATED');

          throw MotivaiException.ofError('TRANSACAO_NAO_CRIADA', `Transação não foi criada corretamente - ${temporaryPointId}`);
        }

        // Marca transação como criada e incrementa total distribuido no client
        await this.temporaryPointsRepository.setTransactionCreated(temporaryPointId, transaction._id);
        await this.companiesRepository.incTotalDistributed(temporaryPoint.ClientId, transaction.TotalAmount.Currency);

        // Se campanha for do tipo 'monte sua campanha', decrementa o saldo reservado
        if (campaign.License === 'MAKE_YOUR_CAMPAIGN') {
          i('Decrementando saldo reservado do cliente', temporaryPointId);
          await this.companiesRepository.decReservedBalance(temporaryPoint.ClientId, transaction.TotalAmount.Currency);
        }
      } else {
        i('Recuperando transação existente', temporaryPointId);
        transaction = await this.transactionRepository.findByTemporaryPointId(temporaryPointId);
      }

      // Valida se encontrou a transação para prosseguir
      if (!transaction) {
        await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, 'Transação não encontrada', 'TRANSACTION_NOT_FOUND');

        throw MotivaiException.ofError('TRANSACAO_NAO_ENCONTRADA', `Transação não foi localizada - ${temporaryPointId}`);
      }

      // Incrementa saldo na mecanica do usuário de acordo com a transação gerada
      // Caso o saldo já tenha sido incrementado, prossegue com os proximos passos
      if (!temporaryPoint.MechanicIncreased) {
        const balanceBefore = await this.mechanicRepository.calculateBalance(temporaryPoint.UserId, temporaryPoint.CampaignId);
        i(`Saldo antes: ${balanceBefore.Points}`, temporaryPointId);

        i('Incrementando mecanica do usuário', temporaryPointId);
        await this.mechanicRepository.incBalanceAmounts(mechanic._id, transaction.TotalAmount, new LegacyAmount(temporaryPoint.AvailableCurrencyAmount, temporaryPoint.AvailablePointsAmount), LegacyAmount.empty());

        // Se a distribuição estiver habilitada, incrementa os totais distribuíveis
        if (mechanic.Distributable) {
          i('Incrementando totais distribuíveis da mecânica', temporaryPointId);
          await this.mechanicRepository.incDistributableAmounts(
            mechanic._id,
            new LegacyAmount(temporaryPoint.DistributableCurrencyAmount, temporaryPoint.DistributablePointsAmount),
            new LegacyAmount(temporaryPoint.DistributableCurrencyAmount, temporaryPoint.DistributablePointsAmount),
          );
        }

        const balanceAfter = await this.mechanicRepository.calculateBalance(temporaryPoint.UserId, temporaryPoint.CampaignId);
        i(`Saldo depois: ${balanceAfter.Points}`, temporaryPointId);

        await this.temporaryPointsRepository.setMechanicIncreased(temporaryPointId, balanceBefore, balanceAfter);

        const _balanceBefore = new LegacyDecimalAmount(balanceBefore?.Points, balanceBefore?.Currency);
        const _balanceAfter = new LegacyDecimalAmount(balanceAfter?.Points, balanceAfter?.Currency);

        await this.transactionRepository.registerBalanceBeforeAndAfter(transaction._id, _balanceBefore, _balanceAfter);
      }

      // Faz integração de pontos com cliente (caso habilitada na campanha)
      // Caso já esteja integrado, prossegue com os próximos passos
      if (!temporaryPoint.Integrated) {
        if (campaign.IntegrationSettings) {
          if (campaign.IntegrationSettings.Integrator !== 'None' && campaign.IntegrationSettings.EnablePointsCredit) {
            i('Creditando pontos no cliente', temporaryPointId);
            const integrated = await this.clientsIntegrationRepository.credit(temporaryPoint.CampaignId, temporaryPoint.ParticipantIdentification, transaction.TotalAmount.Points);
            if (!integrated) {
              await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, 'Ocorreu um erro na integração dos pontos', 'INTEGRATION_ERROR');

              throw MotivaiException.ofError('INTEGRACAO_DE_PONTOS', `Ocorreu um erro na integração dos pontos - ${temporaryPointId}`);
            } else if (!integrated.success) {
              await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, integrated.error || 'Ocorreu um erro na integração dos pontos', 'INTEGRATION_ERROR');

              throw MotivaiException.ofError('INTEGRACAO_DE_PONTOS', `Ocorreu um erro na integração dos pontos - ${temporaryPointId}`);
            } else {
              i('Atualizando transação com detalhes da integração', temporaryPointId);
              await this.temporaryPointsRepository.setIntegrated(temporaryPointId, integrated.return.clientTransactionId || null, integrated.return.clientTransactionCode || null);
              await this.transactionRepository.updateIntegrationDetails(transaction._id, integrated.return.clientTransactionId || null, integrated.return.clientTransactionCode || null);
            }
          }
        }
      }

      // Totaliza ranking de participantes (MechanicTotals, tabela auxiliar para gerar ranking do site campanha)
      // Caso já tenha totalizado, prossegue com os proximos passos
      if (!temporaryPoint.Totalized) {
        i('Incrementando os totais da mecanica (ranking)', temporaryPointId);
        const totalizer = {
          _id: generateUUID(),
          CampaignId: temporaryPoint.CampaignId,
          UserId: temporaryPoint.UserId,
          ParticipantName: temporaryPoint.ParticipantName,
          PersonType: this.handlePersonType(temporaryPoint.ParticipantDocument),
          TotalAmount: transaction.TotalAmount,
        } as MechanicTotal;

        // Incrementa usando upsert e trata caso dê algum erro de processamento paralelo
        try {
          await this.mechanicsTotalsRepository.upsert(totalizer);
        } catch (err) {
          // Erro de duplicidade, tenta realizar um novo upsert
          if (err && err.code && err.code.toString() === '11000') {
            await this.mechanicsTotalsRepository.upsert(totalizer);
          } else {
            throw MotivaiException.ofError('UNEXPECTED_ERROR', `${(err.message || err, err)} - ${temporaryPointId}`);
          }
        }

        await this.temporaryPointsRepository.setTotalized(temporaryPointId);
      }

      // Marca ponto temporário como processado e encerra processo primário
      i('Finalizando processo de crédito', temporaryPointId);
      await this.temporaryPointsRepository.setProcessed(temporaryPointId);
      const batch = await this.batchRepository.incTotalSuccessCompleted(temporaryPoint.BatchImportId);

      // Verifica se batch está finalizado
      const finished = batch.TotalSuccessCompleted + batch.TotalErrorCompleted === batch.TotalSuccessImported;
      if (finished) {
        i('Batch finalizado, atualizando status', temporaryPointId);
        await this.batchRepository.completeBatch(batchId);

        i(`Checando mechanic.EnablePostProcessingTriggerRules: ${temporaryPoint.EnablePostProcessingTriggerRules}`, temporaryPointId);
        if (temporaryPoint.EnablePostProcessingTriggerRules) {
          await this.sendBatchToRaffleQueueProcess(batchId, temporaryPoint);
        }

        if (batch.OnFinish && batch.OnFinish.Queue && batch.OnFinish.Message) {
          await this.callOnFinishCallback(batch, temporaryPointId);
        }
      }

      return transaction;
    } catch (err) {
      this.logger.error(`tracking: ${batchId} - ${err.message} - ${err}`);

      if (!(err instanceof MotivaiException)) {
        await this.temporaryPointsRepository.setErrorMessage(temporaryPointId, err.message, 'UNEXPECTED_ERROR');
      }

      await this.batchRepository.incTotalErrorCompleted(batchId);
      throw err;
    }
  }

  private handlePersonType(document: string) {
    if (!document) return null;
    if (document.length === 11) return PersonType.FISICA;
    if (document.length === 14) return PersonType.JURIDICA;
    return null;
  }

  private async sendBatchToRaffleQueueProcess(batchId: string, temporaryPoint: TemporaryPoints): Promise<void> {
    i(`Enviando batchId: ${batchId} para a fila de raffle`, temporaryPoint._id);
    try {
      const model: SendBatchToRaffleQueue = { batchId, campaignId: temporaryPoint.CampaignId };
      await this.batchRaffleQueueApi.sendBatchToQueue(model);
    } catch (error) {
      i('Erro ao enviar para fila de pós-processamento', temporaryPoint._id);

      this.logger.error(`tracking: ${batchId} - ${error.message} - ${error}`);
      // TODO: registrar erro ou mandar e-mail
    }
  }

  private async callOnFinishCallback(batch: BatchImport, temporaryPointId: string): Promise<void> {
    i('Disparando webhook de finalização', temporaryPointId);
    try {
      i(`Enviando para fila do webhook '${batch.OnFinish.Queue}'`, temporaryPointId);
      const sent = await this.amqpConnection.managedChannel.sendToQueue(batch.OnFinish.Queue, Buffer.from(batch.OnFinish.Message));
      i(`Retorno enviado para fila do webhook: ${sent}`, temporaryPointId);
    } catch (error) {
      this.logger.error(`tracking: ${batch._id} - ${error.message} - ${error}`, MainService.name);
    }
  }
}
