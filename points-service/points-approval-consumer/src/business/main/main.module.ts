import { MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

import { CampaignPointsMetadataHeadersModule } from '../uses-case/campaign-points-metadata-headers/campaign-points-metadata-headers.module';
import { ClientIntegrationModule } from '../uses-case/clients-integration/client-integration.module';
import { BatchRaffleQueueModule } from '../uses-case/raffle-import-batch/raffle-import-batch.module';
import { TemporaryPointsModule } from '../uses-case/temporary-points/temporary-points.module';
import { MechanicTotalsModule } from '../uses-case/mechanics-totals/mechanics-totals.module';
import { TransactionsModule } from '../uses-case/transactions/transactions.module';
import { BatchImportModule } from '../uses-case/batch/batch-import.module';
import { CampaignsModule } from '../uses-case/campaigns/campaigns.module';
import { CompaniesModule } from '../uses-case/companies/companies.module';
import { MechanicModule } from '../uses-case/mechanic/mechanic.module';
import { MotivaiRabbitMQModule } from '../../config/motivai-rabbitmq.module';

import { MainService } from './services/main.service';

@MotivaiNestCommon.Module({
  imports: [
    BatchImportModule,
    MotivaiRabbitMQModule,
    CampaignPointsMetadataHeadersModule,
    CampaignsModule,
    ClientIntegrationModule,
    CompaniesModule,
    MechanicModule,
    MechanicTotalsModule,
    BatchRaffleQueueModule,
    TemporaryPointsModule,
    TransactionsModule,
  ],
  providers: [MainService],
  exports: [MainService],
})
export class MainModule {}
