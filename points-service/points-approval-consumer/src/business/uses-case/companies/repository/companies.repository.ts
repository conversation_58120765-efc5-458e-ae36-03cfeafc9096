import { MotivaiNestMongoose, MotivaiMongoose, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { Company } from '@motivai/motivai-shared-companies';

@MotivaiNestCommon.Injectable()
export class CompaniesRepository {
  constructor(@MotivaiNestMongoose.InjectModel(Company.name) private readonly model: MotivaiMongoose.Model<Company>) {}

  public async incTotalDistributed(companyId: string, amount: number): Promise<any> {
    return await this.model.updateOne(
      { clientId: companyId },
      {
        $inc: {
          'financialDetails.currentDistributedBalance': amount,
          'financialDetails.totalDistributed': amount,
        },
      },
    );
  }

  public async decReservedBalance(companyId: string, amount: number): Promise<any> {
    return await this.model.updateOne(
      { clientId: companyId },
      {
        $inc: {
          'financialDetails.currentDistributedBalance': -amount,
        },
      },
    );
  }
}
