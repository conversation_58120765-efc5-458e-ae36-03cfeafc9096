import { MotivaiNestMongoose, MotivaiMongoose, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { LegacyDecimalAmount } from '@motivai/motivai-shared-mongoose';
import { Transaction } from '@motivai/motivai-shared-transactions';

@MotivaiNestCommon.Injectable()
export class TransactionsRepository {
  constructor(@MotivaiNestMongoose.InjectModel(Transaction.name) private readonly model: MotivaiMongoose.Model<Transaction>) {}

  public async create(transaction: Transaction): Promise<Transaction> {
    return await this.model.create(transaction);
  }

  public async findById(transactionId: string): Promise<Transaction> {
    return await this.model.findById(transactionId);
  }

  public async updateIntegrationDetails(transactionId: string, clientTransactionId: string, clientTransactionCode: string): Promise<void> {
    await this.model.updateOne(
      { _id: transactionId },
      {
        ClientTransactionId: clientTransactionId,
        ClientTransactionCode: clientTransactionCode,
      },
    );
  }

  public async findByTemporaryPointId(temporaryPointId: string): Promise<Transaction> {
    return await this.model.findOne({ TemporaryPointId: temporaryPointId });
  }

  public async registerBalanceBeforeAndAfter(transactionId: string, balanceBefore: LegacyDecimalAmount, balanceAfter: LegacyDecimalAmount): Promise<void> {
    await this.model.updateOne(
      { _id: transactionId },
      {
        BalanceBefore: balanceBefore,
        BalanceAfter: balanceAfter,
      },
    );
  }
}
