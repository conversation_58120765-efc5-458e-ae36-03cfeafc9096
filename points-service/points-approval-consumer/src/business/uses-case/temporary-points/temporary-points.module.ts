import { TemporaryPointsSchemaModule } from '@motivai/motivai-shared-temporary-points';
import { MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

import { TemporaryPointsRepository } from './repository/temporary-points.repository';

@MotivaiNestCommon.Module({
  imports: [TemporaryPointsSchemaModule],
  providers: [TemporaryPointsRepository],
  exports: [TemporaryPointsRepository],
})
export class TemporaryPointsModule {}
