import { MotivaiNestMongoose, MotivaiMongoose, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { TemporaryPoints } from '@motivai/motivai-shared-temporary-points';
import { LegacyAmount } from '@motivai/motivai-shared-mongoose';

@MotivaiNestCommon.Injectable()
export class TemporaryPointsRepository {
  constructor(@MotivaiNestMongoose.InjectModel(TemporaryPoints.name) private readonly model: MotivaiMongoose.Model<TemporaryPoints>) {}

  public async findById(temporaryPointId: string): Promise<TemporaryPoints> {
    return await this.model.findById(temporaryPointId);
  }

  public async setTotalized(temporaryPointId: string): Promise<void> {
    await this.model.updateOne(
      { _id: temporaryPointId },
      {
        $set: {
          Totalized: true,
        },
      },
    );
  }

  public async setTransactionCreated(temporaryPointId: string, transactionId: string): Promise<void> {
    await this.model.updateOne(
      { _id: temporaryPointId },
      {
        $set: {
          TransactionCreated: true,
          GeneratedTransactionId: transactionId,
        },
      },
    );
  }

  public async setMechanicIncreased(temporaryPointId: string, balanceBefore: LegacyAmount, balanceAfter: LegacyAmount): Promise<void> {
    await this.model.updateOne(
      { _id: temporaryPointId },
      {
        $set: {
          MechanicIncreased: true,
          BalanceBefore: balanceBefore,
          BalanceAfter: balanceAfter,
        },
      },
    );
  }

  public async setProcessed(batchId: string): Promise<void> {
    await this.model.updateOne(
      { _id: batchId },
      {
        $set: {
          Processed: true,
        },
      },
    );
  }

  public async setIntegrated(id: string, clientTransactionId: string, clientTransactionCode: string): Promise<void> {
    await this.model.updateOne(
      { _id: id },
      {
        $set: {
          Integrated: true,
          ClientTransactionId: clientTransactionId,
          ClientTransactionCode: clientTransactionCode,
        },
      },
    );
  }

  public async setErrorMessage(id: string, err: string, statusCode: string): Promise<void> {
    await this.model.updateOne(
      { _id: id },
      {
        $set: {
          ErrorMessage: err,
          StatusCode: statusCode,
        },
      },
    );
  }
}
