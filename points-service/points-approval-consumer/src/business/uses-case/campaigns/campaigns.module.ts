import { MotivaiSharedCoreCampaignsModule } from '@motivai/motivai-shared-campaign';
import { CacheModule } from '@motivai/motivai-shared-aws-services';
import { MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

import { CampaignsRepository } from './repository/campaign.repository';

@MotivaiNestCommon.Module({
  imports: [MotivaiSharedCoreCampaignsModule, CacheModule],
  providers: [CampaignsRepository],
  exports: [CampaignsRepository],
})
export class CampaignsModule {}
