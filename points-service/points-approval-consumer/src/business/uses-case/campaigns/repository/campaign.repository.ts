import { MotivaiNestMongoose, MotivaiMongoose, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { CacheService } from '@motivai/motivai-shared-aws-services';
import { Campaign } from '@motivai/motivai-shared-campaign';

@MotivaiNestCommon.Injectable()
export class CampaignsRepository {
  constructor(@MotivaiNestMongoose.InjectModel(Campaign.name) private readonly model: MotivaiMongoose.Model<Campaign>, private readonly cacher: CacheService) {}

  public async findById(campaignId: string): Promise<Campaign> {
    const cacheKey = `points-engine:campaign:${campaignId}`;

    let campaign = await this.cacher.get(cacheKey);

    if (campaign) {
      return campaign;
    }

    campaign = await this.cacher.getOrCreateObject<Campaign | null>(cacheKey, async () => await this.model.findById(campaignId).select('_id License IntegrationSettings').exec(), 604800);

    return campaign;
  }
}
