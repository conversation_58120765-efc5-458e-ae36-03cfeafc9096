import { MotivaiNestMongoose, MotivaiMongoose, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { toBinaryUUID, LegacyAmount } from '@motivai/motivai-shared-mongoose';
import { Mechanic } from '@motivai/motivai-shared-mechanics';

import * as moment from 'moment';

@MotivaiNestCommon.Injectable()
export class MechanicRepository {
  constructor(@MotivaiNestMongoose.InjectModel(Mechanic.name) private readonly model: MotivaiMongoose.Model<Mechanic>) {}

  public async findUserMechanic(userId: string, campaignId: string, templateId: string, batchId?: string): Promise<Mechanic> {
    if (!batchId) {
      return await this.model.findOne({ UserId: userId, CampaignId: campaignId, TemplateId: templateId });
    } else {
      return await this.model.findOne({ UserId: userId, CampaignId: campaignId, TemplateId: templateId, BatchId: batchId });
    }
  }

  public async find(id: string): Promise<Mechanic> {
    return await this.model.findById(id);
  }

  public async create(mechanic: Mechanic): Promise<Mechanic> {
    return await this.model.create(mechanic);
  }

  public async calculateBalance(userId: string, campaignId: string): Promise<LegacyAmount> {
    const balance = await this.model
      .aggregate()
      .match({
        CampaignId: toBinaryUUID(campaignId),
        UserId: toBinaryUUID(userId),
        ExpirationDate: {
          $gte: moment.utc().toDate(),
        },
      })
      .group({
        _id: '$UserId',
        Points: { $sum: '$AvailableAmount.Points' },
        Currency: { $sum: '$AvailableAmount.Currency' },
      })
      .project({
        _id: 0,
        Points: { $substr: ['$Points', 0, 999] },
        Currency: { $substr: ['$Currency', 0, 999] },
      })
      .exec();
    if (!balance || !balance.length) {
      return { Points: 0, Currency: 0 } as LegacyAmount;
    }
    return {
      Points: parseFloat(balance[0]?.Points),
      Currency: parseFloat(balance[0]?.Currency),
    } as LegacyAmount;
  }

  public async incBalanceAmounts(mechanicId: string, totalAmount: LegacyAmount, availableAmount: LegacyAmount, blockedAmount: LegacyAmount): Promise<void> {
    await this.model.updateOne(
      { _id: mechanicId },
      {
        $inc: {
          'TotalAmount.Currency': totalAmount?.Currency,
          'TotalAmount.Points': totalAmount?.Points,
          'AvailableAmount.Currency': availableAmount?.Currency,
          'AvailableAmount.Points': availableAmount?.Points,
          'BlockedAmount.Currency': blockedAmount?.Currency,
          'BlockedAmount.Points': blockedAmount?.Points,
        },
      },
    );
  }

  public async incDistributableAmounts(mechanicId: string, totalAmount: LegacyAmount, availableDistributableAmount: LegacyAmount): Promise<void> {
    await this.model.updateOne(
      { _id: mechanicId },
      {
        $inc: {
          'DistributableConfig.TotalAmount.Currency': totalAmount?.Currency,
          'DistributableConfig.TotalAmount.Points': totalAmount?.Points,
          'DistributableConfig.AvailableDistributableAmount.Currency': availableDistributableAmount?.Currency,
          'DistributableConfig.AvailableDistributableAmount.Points': availableDistributableAmount?.Points,
        },
      },
    );
  }
}
