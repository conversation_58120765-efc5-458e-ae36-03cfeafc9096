import { MotivaiNestMongoose, MotivaiMongoose, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { MechanicTotal } from '@motivai/motivai-shared-mechanics';

@MotivaiNestCommon.Injectable()
export class MechanicsTotalsRepository {
  constructor(@MotivaiNestMongoose.InjectModel(MechanicTotal.name) private readonly model: MotivaiMongoose.Model<MechanicTotal>) {}

  public async upsert(mechanicTotal: MechanicTotal): Promise<void> {
    this.model.updateOne(
      { UserId: mechanicTotal.UserId, CampaignId: mechanicTotal.CampaignId },
      {
        ParticipantName: mechanicTotal.ParticipantName,
        PersonType: mechanicTotal.PersonType,
        $inc: {
          'TotalAmount.Currency': mechanicTotal?.TotalAmount?.Currency,
          'TotalAmount.Points': mechanicTotal?.TotalAmount?.Points,
        },
      },
      { upsert: true },
    );
  }
}
