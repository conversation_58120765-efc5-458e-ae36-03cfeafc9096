import { MotivaiAxios, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

import { SendBatchToRaffleQueue } from '../models/send-batch-to-raffle-queue.model';

@MotivaiNestCommon.Injectable()
export class BatchRaffleQueueApi {
  public async sendBatchToQueue(sendBatchToRaffleQueue: SendBatchToRaffleQueue): Promise<void> {
    const uri: string = `${process.env.RAFFLE_API}/raffles/points-import/batches/queuing`;

    await MotivaiAxios.default.post(uri, sendBatchToRaffleQueue, {
      headers: {
        'x-api-key': process.env.MOTIVAI_API_KEY,
      },
    } as MotivaiAxios.AxiosRequestConfig);
  }
}
