import { MotivaiNestMongoose, MotivaiMongoose, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { BatchImport } from '@motivai/motivai-shared-batch-import';

@MotivaiNestCommon.Injectable()
export class BatchRepository {
  constructor(@MotivaiNestMongoose.InjectModel(BatchImport.name) private readonly model: MotivaiMongoose.Model<BatchImport>) {}

  public async incTotalErrorCompleted(batchImportId: string, totalErrorCompleted: number = 1): Promise<BatchImport> {
    return await this.model.findOneAndUpdate(
      { _id: batchImportId },
      {
        $inc: {
          TotalErrorCompleted: totalErrorCompleted,
        },
      },
      { new: true },
    );
  }

  public async incTotalSuccessCompleted(batchImportId: string, totalSuccessCompleted: number = 1): Promise<BatchImport> {
    return await this.model.findOneAndUpdate(
      { _id: batchImportId },
      {
        $inc: {
          TotalSuccessCompleted: totalSuccessCompleted,
        },
      },
      { new: true },
    );
  }

  public async completeBatch(batchId: string): Promise<any> {
    return await this.model.updateOne(
      { _id: batchId },
      {
        $set: {
          Status: 'Completed',
          Success: true,
          Active: false,
        },
      },
    );
  }
}
