import { CampaignPointsMetadataHeader, HeaderDetail } from '@motivai/motivai-shared-campaign-points-metadata-header';
import { MotivaiNestMongoose, MotivaiMongoose, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

@MotivaiNestCommon.Injectable()
export class CampaignPointsMetadataHeadersRepository {
  constructor(@MotivaiNestMongoose.InjectModel(CampaignPointsMetadataHeader.name) private readonly model: MotivaiMongoose.Model<CampaignPointsMetadataHeader>) {}

  public async getById(id: string): Promise<CampaignPointsMetadataHeader> {
    return await this.model.findById(id);
  }

  public async createMetadata(metadata: CampaignPointsMetadataHeader): Promise<CampaignPointsMetadataHeader> {
    return await this.model.create(metadata);
  }

  public async pushHeader(metadataId: string, header: HeaderDetail): Promise<void> {
    await this.model.updateOne(
      { _id: metadataId },
      {
        $push: {
          HeadersDetails: header,
        },
      },
    );
  }
}
