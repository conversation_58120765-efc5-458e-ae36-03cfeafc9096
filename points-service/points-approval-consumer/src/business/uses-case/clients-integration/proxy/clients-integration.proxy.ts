import { MotivaiAxios, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

@MotivaiNestCommon.Injectable()
export class ClientsIntegrationProxy {
  public async credit(campaignId: string, identification: string, amount: number) {
    const url = `${process.env.API_CLIENTS_INTEGRATIONS}/clients/campaigns/${campaignId}/points/credit`;
    const integrated = await MotivaiAxios.default.post(url, {
      campaignId,
      identification,
      amount,
    });

    return integrated.data;
  }
}
