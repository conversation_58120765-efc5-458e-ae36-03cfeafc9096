import * as dotenv from 'dotenv';
dotenv.config();

import { MotivaiNestCore, MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';
import { AppModule } from './app.module';

async function bootstrap() {
  const PORT: string = process.env.PORT || '3000';

  MotivaiNestCommon.Logger.log(`LISTENING PORT: ${PORT}`, 'APPLICATION-START');

  const app = await MotivaiNestCore.NestFactory.create(AppModule);
  await app.listen(PORT);
}
bootstrap();
