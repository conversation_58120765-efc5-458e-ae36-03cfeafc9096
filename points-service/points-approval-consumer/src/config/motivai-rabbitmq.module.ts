import { RabbitMQQueueConfig, RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { MotivaiNestCommon } from '@motivai/motivai-shared-nestjs';

const queue = process.env.QUEUE_POINTS_APPROVED_CREDITS;
const dlQueue = process.env.DL_POINTS_APPROVED_CREDITS;

MotivaiNestCommon.Logger.log(`LISTENING QUEUE: ${queue} - DL - ${dlQueue}`, 'RABBITMQ-CONFIG');

@MotivaiNestCommon.Module({
  imports: [
    RabbitMQModule.forRoot(RabbitMQModule, {
      queues: [
        {
          name: queue,
          options: { deadLetterExchange: dlQueue },
        } as RabbitMQQueueConfig,
      ] as RabbitMQQueueConfig[],
      uri: `${process.env.RABBITMQ_URI}/points-import`,
      channels: {
        'channel-1': {
          prefetchCount: 1,
          default: true
        }
      },
      connectionInitOptions: { wait: false },
    }),
  ],
  providers: [],
  exports: [RabbitMQModule],
})
export class MotivaiRabbitMQModule {}
