import { MotivaiNestCommon, MotivaiNestConfig } from '@motivai/motivai-shared-nestjs';
import { MotivaiMongooseConnection } from '@motivai/motivai-shared-mongoose';
import * as path from 'path';

import { PointsApprovalWorkerModule } from './workers/points-approval-worker.module';
import { PointsApprovalApiModule } from './api/points-approval-api.module';

@MotivaiNestCommon.Module({
  imports: [
    MotivaiNestConfig.ConfigModule.forRoot({
      envFilePath: path.resolve(
        process.cwd(),
        `.env.${process.env.NODE_ENV || 'development'}`, // Seleciona o arquivo com base no NODE_ENV
      ),
      isGlobal: true, // Torna o ConfigModule acessível globalmente na aplicação
    }),
    PointsApprovalWorkerModule,
    MotivaiMongooseConnection,
    PointsApprovalApiModule,
  ],
  providers: [],
})
export class AppModule {}
